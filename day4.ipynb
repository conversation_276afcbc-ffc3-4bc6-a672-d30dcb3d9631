!pip install scikit-learn

import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn import metrics
import matplotlib.pyplot as plt
import seaborn as sns

# Load the dataset from the CSV file
df = pd.read_csv('USA_Housing_toy.csv')

# X contains all the columns we will use as features to predict the price
X = df[['Avg. Area Income', 'Avg. Area House Age', 'Avg. Area Number of Rooms',
        'Avg. Area Number of Bedrooms', 'Area Population']]

# y is the target variable, the 'Price' column, which we want to predict
y = df['Price']

# You can print the first few rows of X to see what it looks like
print("Features (X):")
print(X.head())